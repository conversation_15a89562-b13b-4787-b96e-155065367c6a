# See docs/devel/tracing.rst for syntax documentation.

# rc4030.c
jazzio_read(uint64_t addr, uint32_t ret) "read reg[0x%"PRIx64"] = 0x%x"
jazzio_write(uint64_t addr, uint32_t val) "write reg[0x%"PRIx64"] = 0x%x"
rc4030_read(uint64_t addr, uint32_t ret) "read reg[0x%"PRIx64"] = 0x%x"
rc4030_write(uint64_t addr, uint32_t val) "write reg[0x%"PRIx64"] = 0x%x"

# sparc32_dma.c
ledma_memory_read(uint64_t addr, int len) "DMA read addr 0x%"PRIx64 " len %d"
ledma_memory_write(uint64_t addr, int len) "DMA write addr 0x%"PRIx64 " len %d"
sparc32_dma_set_irq_raise(void) "Raise IRQ"
sparc32_dma_set_irq_lower(void) "Lower IRQ"
espdma_memory_read(uint32_t addr, int len) "DMA read addr 0x%08x len %d"
espdma_memory_write(uint32_t addr, int len) "DMA write addr 0x%08x len %d"
sparc32_dma_mem_readl(uint64_t addr, uint32_t ret) "read dmareg 0x%"PRIx64": 0x%08x"
sparc32_dma_mem_writel(uint64_t addr, uint32_t old, uint32_t val) "write dmareg 0x%"PRIx64": 0x%08x -> 0x%08x"
sparc32_dma_enable_raise(void) "Raise DMA enable"
sparc32_dma_enable_lower(void) "Lower DMA enable"

# i8257.c
i8257_unregistered_dma(int nchan, int dma_pos, int dma_len) "unregistered DMA channel used nchan=%d dma_pos=%d dma_len=%d"

# pl330.c
pl330_fault(void *ptr, uint32_t flags) "ch: %p, flags: 0x%"PRIx32
pl330_fault_abort(void) "abort interrupt raised"
pl330_dmaend(void) "DMA ending"
pl330_dmago(void) "DMA run"
pl330_dmald(uint8_t chan, uint32_t addr, uint32_t size, uint32_t num, char ch) "channel:%"PRId8" address:0x%08"PRIx32" size:0x%"PRIx32" num:%"PRId32"%c"
pl330_dmakill(void) "abort interrupt lowered"
pl330_dmalpend(uint8_t nf, uint8_t bs, uint8_t lc, uint8_t ch, uint8_t flag) "nf=0x%02x bs=0x%02x lc=0x%02x ch=0x%02x flag=0x%02x"
pl330_dmalpiter(void) "loop reiteration"
pl330_dmalpfallthrough(void) "loop fallthrough"
pl330_dmasev_evirq(uint8_t ev_id) "event interrupt raised %"PRId8
pl330_dmasev_event(uint8_t ev_id) "event raised %"PRId8
pl330_dmast(uint8_t chan, uint32_t addr, uint32_t sz, uint32_t num, char ch) "channel:%"PRId8" address:0x%08"PRIx32" size:0x%"PRIx32" num:%"PRId32" %c"
pl330_dmawfe(uint8_t ev_id) "event lowered 0x%"PRIx8
pl330_chan_exec_undef(void) "undefined instruction"
pl330_exec_cycle(uint32_t addr, uint32_t size) "PL330 read from memory @0x%08"PRIx32" (size = 0x%08"PRIx32")"
pl330_hexdump(uint32_t offset, char *str) " 0x%04"PRIx32":%s"
pl330_exec(void) "pl330_exec"
pl330_debug_exec(uint8_t ch) "chan id: 0x%"PRIx8
pl330_debug_exec_stall(void) "stall of debug instruction not implemented"
pl330_iomem_write(uint32_t offset, uint32_t value) "addr: 0x%08"PRIx32" data: 0x%08"PRIx32
pl330_iomem_write_clr(int i) "event interrupt lowered %d"
pl330_iomem_read(uint32_t addr, uint32_t data) "addr: 0x%08"PRIx32" data: 0x%08"PRIx32

# xilinx_axidma.c
xilinx_axidma_loading_desc_fail(uint32_t res) "error:%u"
