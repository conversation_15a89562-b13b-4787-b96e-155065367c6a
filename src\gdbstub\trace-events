# See docs/devel/tracing.rst for syntax documentation.

# gdbstub.c
gdbstub_op_start(const char *device) "Starting gdbstub using device %s"
gdbstub_op_exiting(uint8_t code) "notifying exit with code=0x%02x"
gdbstub_op_continue(void) "Continuing all CPUs"
gdbstub_op_continue_cpu(int cpu_index) "Continuing CPU %d"
gdbstub_op_stepping(int cpu_index) "Stepping CPU %d"
gdbstub_op_extra_info(const char *info) "Thread extra info: %s"
gdbstub_hit_internal_error(void) "RUN_STATE_INTERNAL_ERROR"
gdbstub_hit_break(void) "RUN_STATE_DEBUG"
gdbstub_hit_paused(void) "RUN_STATE_PAUSED"
gdbstub_hit_shutdown(void) "RUN_STATE_SHUTDOWN"
gdbstub_hit_io_error(void) "RUN_STATE_IO_ERROR"
gdbstub_hit_watchdog(void) "RUN_STATE_WATCHDOG"
gdbstub_hit_unknown(int state) "Unknown run state=0x%x"
gdbstub_io_reply(const char *message) "Sent: %s"
gdbstub_io_binaryreply(size_t ofs, const char *line) "0x%04zx: %s"
gdbstub_io_command(const char *command) "Received: %s"
gdbstub_io_got_ack(void) "Got ACK"
gdbstub_io_got_unexpected(uint8_t ch) "Got 0x%02x when expecting ACK/NACK"
gdbstub_err_got_nack(void) "Got NACK, retransmitting"
gdbstub_err_garbage(uint8_t ch) "received garbage between packets: 0x%02x"
gdbstub_err_overrun(void) "command buffer overrun, dropping command"
gdbstub_err_invalid_repeat(uint8_t ch) "got invalid RLE count: 0x%02x"
gdbstub_err_invalid_rle(void) "got invalid RLE sequence"
gdbstub_err_checksum_invalid(uint8_t ch) "got invalid command checksum digit: 0x%02x"
gdbstub_err_checksum_incorrect(uint8_t expected, uint8_t got) "got command packet with incorrect checksum, expected=0x%02x, received=0x%02x"
gdbstub_err_unexpected_runpkt(uint8_t ch) "unexpected packet (0x%02x) while target running"

# system.c
gdbstub_hit_watchpoint(const char *type, int cpu_gdb_index, uint64_t vaddr) "Watchpoint hit, type=\"%s\" cpu=%d, vaddr=0x%" PRIx64 ""
