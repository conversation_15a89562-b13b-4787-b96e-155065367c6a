/*
 * ACPI stubs for platforms that don't support ACPI.
 *
 * Copyright (c) 2006 Fabrice Bellard
 * Copyright (c) 2016 Red Hat, Inc.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program; if not, see <http://www.gnu.org/licenses/>.
 */

#include "qemu/osdep.h"
#include "hw/acpi/acpi.h"

char unsigned *acpi_tables;
size_t acpi_tables_len;

void acpi_table_add(const QemuOpts *opts, Error **errp)
{
    g_assert_not_reached();
}

bool acpi_builtin(void)
{
    return false;
}
