<?xml version="1.0"?>
<!-- Copyright (C) 2010-2014 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.s390.core">
  <reg name="pswm" bitsize="64" type="uint64" group="psw"/>
  <reg name="pswa" bitsize="64" type="uint64" group="psw"/>
  <reg name="r0" bitsize="64" type="uint64" group="general"/>
  <reg name="r1" bitsize="64" type="uint64" group="general"/>
  <reg name="r2" bitsize="64" type="uint64" group="general"/>
  <reg name="r3" bitsize="64" type="uint64" group="general"/>
  <reg name="r4" bitsize="64" type="uint64" group="general"/>
  <reg name="r5" bitsize="64" type="uint64" group="general"/>
  <reg name="r6" bitsize="64" type="uint64" group="general"/>
  <reg name="r7" bitsize="64" type="uint64" group="general"/>
  <reg name="r8" bitsize="64" type="uint64" group="general"/>
  <reg name="r9" bitsize="64" type="uint64" group="general"/>
  <reg name="r10" bitsize="64" type="uint64" group="general"/>
  <reg name="r11" bitsize="64" type="uint64" group="general"/>
  <reg name="r12" bitsize="64" type="uint64" group="general"/>
  <reg name="r13" bitsize="64" type="uint64" group="general"/>
  <reg name="r14" bitsize="64" type="uint64" group="general"/>
  <reg name="r15" bitsize="64" type="uint64" group="general"/>
</feature>
