kvm_xen_map_pirq(int pirq, int gsi) "pirq %d gsi %d"
kvm_xen_unmap_pirq(int pirq, int gsi) "pirq %d gsi %d"
kvm_xen_get_free_pirq(int pirq, int type) "pirq %d type %d"
kvm_xen_bind_pirq(int pirq, int port) "pirq %d port %d"
kvm_xen_unmask_pirq(int pirq, char *dev, int vector) "pirq %d dev %s vector %d"
xenstore_error(unsigned int id, unsigned int tx_id, const char *err) "req %u tx %u err %s"
xenstore_read(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_write(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_mkdir(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_directory(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_directory_part(unsigned int tx_id, const char *path, unsigned int offset) "tx %u path %s offset %u"
xenstore_transaction_start(unsigned int new_tx) "new_tx %u"
xenstore_transaction_end(unsigned int tx_id, bool commit) "tx %u commit %d"
xenstore_rm(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_get_perms(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_set_perms(unsigned int tx_id, const char *path) "tx %u path %s"
xenstore_watch(const char *path, const char *token) "path %s token %s"
xenstore_unwatch(const char *path, const char *token) "path %s token %s"
xenstore_reset_watches(void) ""
xenstore_watch_event(const char *path, const char *token) "path %s token %s"
xen_primary_console_create(void) ""
xen_primary_console_reset(int port) "port %u"
