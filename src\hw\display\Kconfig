config DDC
    bool
    depends on I2C
    select EDID

config EDID
    bool

config FW_CFG_DMA
    bool

config VGA_CIRRUS
    bool
    default y if PCI_DEVICES
    depends on PCI
    select VGA

config G364FB
    bool

config JAZZ_LED
    bool

config PL110
    bool
    select FRAMEBUFFER

config SII9022
    bool
    depends on I2C
    select DDC

config SSD0303
    bool
    depends on I2C

config SSD0323
    bool

config VGA_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI
    select VGA
    select EDID

config VGA_ISA
    bool
    depends on ISA_BUS
    select VGA

config VGA_MMIO
    bool
    select VGA

config VMWARE_VGA
    bool
    default y if PCI_DEVICES && (PC_PCI || MIPS)
    depends on PCI
    select VGA

config BOCHS_DISPLAY
    bool
    default y if PCI_DEVICES
    depends on PCI
    select VGA
    select EDID

config FRAMEBUFFER
    bool

config SM501
    bool
    select I2C
    select DDC
    select SERIAL_MM
    select USB_OHCI_SYSBUS

config TCX
    bool

config CG3
    bool

config ARTIST
    bool
    select F<PERSON><PERSON>BUFFER

config VGA
    bool

config QXL
    bool
    depends on SPICE && PCI && PIXMAN
    select VGA

config VIRTIO_GPU
    bool
    default y
    depends on VIRTIO
    select EDID

config VIRTIO_VGA
    bool
    # defaults to "N", enabled by specific boards
    depends on VIRTIO_PCI
    select VGA

config VHOST_USER_GPU
    bool
    default y
    depends on VIRTIO_GPU && VHOST_USER

config VHOST_USER_VGA
    bool
    default y
    depends on VIRTIO_VGA && VHOST_USER_GPU

config DPCD
    bool
    select AUX

config ATI_VGA
    bool
    default y if PCI_DEVICES
    depends on PCI
    select VGA
    select BITBANG_I2C
    select DDC

config MACFB
    bool
    select FRAMEBUFFER
    depends on NUBUS

config XLNX_DISPLAYPORT
    bool
    # defaults to "N", enabled by specific boards
    depends on PIXMAN

config DM163
    bool

config MAC_PVG_MMIO
    bool
    depends on MAC_PVG && AARCH64

config MAC_PVG_PCI
    bool
    depends on MAC_PVG && PCI
    default y if PCI_DEVICES
