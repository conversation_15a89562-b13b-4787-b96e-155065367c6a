<?xml version="1.0"?>
<!-- Copyright (C) 2010-2014 Free Software Foundation, Inc.

     Copying and distribution of this file, with or without modification,
     are permitted in any medium without royalty provided the copyright
     notice and this notice are preserved.  -->

<!DOCTYPE feature SYSTEM "gdb-target.dtd">
<feature name="org.gnu.gdb.s390.fpr">
  <reg name="fpc" bitsize="32" type="uint32" group="float"/>
  <reg name="f0" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f1" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f2" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f3" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f4" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f5" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f6" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f7" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f8" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f9" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f10" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f11" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f12" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f13" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f14" bitsize="64" type="ieee_double" group="float"/>
  <reg name="f15" bitsize="64" type="ieee_double" group="float"/>
</feature>
