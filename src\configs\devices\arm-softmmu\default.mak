# Default configuration for arm-softmmu

# Uncomment the following lines to disable these optional devices:
# CONFIG_I2C_DEVICES=n
# CONFIG_PCI_DEVICES=n
# CONFIG_TEST_DEVICES=n

# Boards are selected by default, uncomment to keep out of the build.
# CONFIG_ARM_VIRT=n

# These are selected by default when TCG is enabled, uncomment them to
# keep out of the build.
# CONFIG_CUBIEBOARD=n
# CONFIG_EXYNOS4=n
# CONFIG_HIGHBANK=n
# CONFIG_INTEGRATOR=n
# CONFIG_FSL_IMX31=n
# CONFIG_MUSICPAL=n
# CONFIG_MPS3R=n
# CONFIG_MUSCA=n
# CONFIG_SX1=n
# CONFIG_STELLARIS=n
# CONFIG_STM32VLDISCOVERY=n
# CONFIG_B_L475E_IOT01A=n
# CONFIG_REALVIEW=n
# CONFIG_VERSATILE=n
# CONFIG_VEXPRESS=n
# CONFIG_ZYNQ=n
# CONFIG_NPCM7XX=n
# CONFIG_COLLIE=n
# CONFIG_ASPEED_SOC=n
# CONFIG_NETDUINO2=n
# CONFIG_NETDUINOPLUS2=n
# CONFIG_OLIMEX_STM32_H405=n
# CONFIG_MPS2=n
# CONFIG_RASPI=n
# CONFIG_DIGIC=n
# CONFIG_SABRELITE=n
# CONFIG_EMCRAFT_SF2=n
# CONFIG_MICROBIT=n
# CONFIG_FSL_IMX25=n
# CONFIG_FSL_IMX7=n
# CONFIG_FSL_IMX6UL=n
# CONFIG_ALLWINNER_H3=n
