config DP8393X
    bool

config NE2000_COMMON
    bool

config NE2000_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI
    select NE2000_COMMON

config EEPRO100_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI
    select NMC93XX_EEPROM

config PCNET_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI
    select PCNET_COMMON

config PCNET_COMMON
    bool

config TULIP
    bool
    default y if PCI_DEVICES
    depends on PCI
    select NMC93XX_EEPROM

config I82596_COMMON
    bool

config E1000_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI

config E1000E_PCI_EXPRESS
    bool
    default y if PCI_DEVICES || PCIE_DEVICES
    depends on PCI_EXPRESS && MSI_NONBROKEN

config IGB_PCI_EXPRESS
    bool
    default y if PCI_DEVICES || PCIE_DEVICES
    depends on PCI_EXPRESS && MSI_NONBR<PERSON><PERSON>

config RTL8139_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI

config VMXNET3_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI

config SMC91C111
    bool

config LAN9118_PHY
    bool

config LAN9118
    bool
    select LAN9118_PHY
    select PTIMER

config NE2000_ISA
    bool
    default y
    depends on ISA_BUS
    select NE2000_COMMON

config OPENCORES_ETH
    bool

config XGMAC
    bool

config MIPSNET
    bool

config ALLWINNER_EMAC
    bool

config ALLWINNER_SUN8I_EMAC
    bool

config IMX_FEC
    bool
    select LAN9118_PHY

config CADENCE
    bool

config STELLARIS_ENET
    bool

config LANCE
    bool
    select PCNET_COMMON

config LASI_82596
    bool
    select I82596_COMMON

config SUNHME
    bool

config FTGMAC100
    bool

config SUNGEM
    bool
    depends on PCI

config COLDFIRE
    bool

config XILINX_ETHLITE
    bool

config VIRTIO_NET
    bool
    default y
    depends on VIRTIO

config ETSEC
    bool
    select PTIMER

config ROCKER
    bool
    default y if PCI_DEVICES
    depends on PCI && MSI_NONBROKEN

config CAN_BUS
    bool

config CAN_SJA1000
    bool
    default y if PCI_DEVICES
    select CAN_BUS

config CAN_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI && CAN_SJA1000
    select CAN_BUS

config CAN_CTUCANFD
    bool
    default y if PCI_DEVICES
    select CAN_BUS

config CAN_CTUCANFD_PCI
    bool
    default y if PCI_DEVICES
    depends on PCI && CAN_CTUCANFD
    select CAN_BUS
