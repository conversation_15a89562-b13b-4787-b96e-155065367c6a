config EMPTY_SLOT
    bool

config PTIMER
    bool

config DEVICE_TREE
    bool
    # fail the build if libfdt not found
    depends on FDT

config FITLOADER
    bool
    depends on DEVICE_TREE

config GENERIC_LOADER
    bool
    default y

config GUEST_LOADER
    bool
    default y
    depends on TCG && DEVICE_TREE

config OR_IRQ
    bool

config PLATFORM_BUS
    bool
    depends on DEVICE_TREE

config REGISTER
    bool

config SPLIT_IRQ
    bool

config EIF
    bool
    depends on LIBCBOR && GNUTLS
